#!/usr/bin/env python3
"""
Machine Learning Model Training Module for NIDS
Trains and evaluates models for network intrusion detection
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.linear_model import LogisticRegression
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import logging
import os
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NIDSModelTrainer:
    def __init__(self, models_dir="models"):
        """
        Initialize the model trainer
        
        Args:
            models_dir: Directory to save trained models
        """
        self.models_dir = models_dir
        self.models = {}
        self.model_metrics = {}
        
        # Create models directory if it doesn't exist
        os.makedirs(models_dir, exist_ok=True)
        
        # Initialize models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize different ML models for comparison"""
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'isolation_forest': IsolationForest(
                contamination=0.1,  # Expect 10% anomalies
                random_state=42,
                n_jobs=-1
            ),
            'logistic_regression': LogisticRegression(
                random_state=42,
                max_iter=1000
            )
        }
    
    def train_supervised_models(self, X_train, X_test, y_train, y_test):
        """
        Train supervised learning models
        
        Args:
            X_train, X_test: Training and testing features
            y_train, y_test: Training and testing labels
            
        Returns:
            dict: Trained models and their metrics
        """
        logger.info("Training supervised models...")
        
        supervised_models = ['random_forest', 'logistic_regression']
        results = {}
        
        for model_name in supervised_models:
            logger.info(f"Training {model_name}...")
            
            model = self.models[model_name]
            
            # Train the model
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
            
            # Store results
            results[model_name] = {
                'model': model,
                'metrics': metrics,
                'predictions': y_pred
            }
            
            # Log results
            logger.info(f"{model_name} - Accuracy: {metrics['accuracy']:.4f}, "
                       f"Precision: {metrics['precision']:.4f}, "
                       f"Recall: {metrics['recall']:.4f}, "
                       f"F1: {metrics['f1_score']:.4f}")
        
        return results
    
    def train_unsupervised_models(self, X_train, X_test, y_test):
        """
        Train unsupervised learning models (anomaly detection)
        
        Args:
            X_train: Training features (no labels needed)
            X_test: Testing features
            y_test: Testing labels (for evaluation only)
            
        Returns:
            dict: Trained models and their metrics
        """
        logger.info("Training unsupervised models...")
        
        unsupervised_models = ['isolation_forest']
        results = {}
        
        for model_name in unsupervised_models:
            logger.info(f"Training {model_name}...")
            
            model = self.models[model_name]
            
            # Train on normal data only (label 0)
            # In real scenarios, you might not have labels during training
            model.fit(X_train)
            
            # Make predictions (-1 for anomaly, 1 for normal)
            y_pred_raw = model.predict(X_test)
            
            # Convert to binary labels (0 for normal, 1 for anomaly)
            y_pred = np.where(y_pred_raw == -1, 1, 0)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, y_pred)
            
            # Store results
            results[model_name] = {
                'model': model,
                'metrics': metrics,
                'predictions': y_pred
            }
            
            # Log results
            logger.info(f"{model_name} - Accuracy: {metrics['accuracy']:.4f}, "
                       f"Precision: {metrics['precision']:.4f}, "
                       f"Recall: {metrics['recall']:.4f}, "
                       f"F1: {metrics['f1_score']:.4f}")
        
        return results
    
    def _calculate_metrics(self, y_true, y_pred, y_pred_proba=None):
        """
        Calculate various performance metrics
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            y_pred_proba: Predicted probabilities (optional)
            
        Returns:
            dict: Performance metrics
        """
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='binary'),
            'recall': recall_score(y_true, y_pred, average='binary'),
            'f1_score': f1_score(y_true, y_pred, average='binary')
        }
        
        # Add AUC if probabilities are available
        if y_pred_proba is not None:
            metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
        
        return metrics
    
    def hyperparameter_tuning(self, X_train, y_train, model_name='random_forest'):
        """
        Perform hyperparameter tuning using GridSearchCV
        
        Args:
            X_train: Training features
            y_train: Training labels
            model_name: Name of model to tune
            
        Returns:
            Best model with tuned parameters
        """
        logger.info(f"Performing hyperparameter tuning for {model_name}...")
        
        if model_name == 'random_forest':
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
            base_model = RandomForestClassifier(random_state=42, n_jobs=-1)
            
        elif model_name == 'logistic_regression':
            param_grid = {
                'C': [0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
            base_model = LogisticRegression(random_state=42, max_iter=1000)
        
        else:
            logger.warning(f"Hyperparameter tuning not implemented for {model_name}")
            return self.models[model_name]
        
        # Perform grid search
        grid_search = GridSearchCV(
            base_model,
            param_grid,
            cv=5,
            scoring='f1',
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        logger.info(f"Best parameters for {model_name}: {grid_search.best_params_}")
        logger.info(f"Best cross-validation score: {grid_search.best_score_:.4f}")
        
        return grid_search.best_estimator_
    
    def save_model(self, model, model_name, metrics=None):
        """
        Save trained model and its metadata
        
        Args:
            model: Trained model object
            model_name: Name of the model
            metrics: Performance metrics (optional)
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model
        model_filename = f"{model_name}_{timestamp}.joblib"
        model_path = os.path.join(self.models_dir, model_filename)
        joblib.dump(model, model_path)
        
        # Save metadata
        metadata = {
            'model_name': model_name,
            'timestamp': timestamp,
            'model_file': model_filename,
            'metrics': metrics or {}
        }
        
        metadata_filename = f"{model_name}_{timestamp}_metadata.json"
        metadata_path = os.path.join(self.models_dir, metadata_filename)
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Model saved: {model_path}")
        logger.info(f"Metadata saved: {metadata_path}")
        
        return model_path, metadata_path
    
    def load_model(self, model_path):
        """
        Load a trained model
        
        Args:
            model_path: Path to the model file
            
        Returns:
            Loaded model object
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        model = joblib.load(model_path)
        logger.info(f"Model loaded from: {model_path}")
        
        return model
    
    def evaluate_model(self, model, X_test, y_test, model_name="model"):
        """
        Comprehensive model evaluation
        
        Args:
            model: Trained model
            X_test: Test features
            y_test: Test labels
            model_name: Name of the model
            
        Returns:
            dict: Evaluation results
        """
        logger.info(f"Evaluating {model_name}...")
        
        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = None
        
        if hasattr(model, 'predict_proba'):
            y_pred_proba = model.predict_proba(X_test)[:, 1]
        elif hasattr(model, 'decision_function'):
            y_pred_proba = model.decision_function(X_test)
        
        # Calculate metrics
        metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
        
        # Generate classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Generate confusion matrix
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        evaluation_results = {
            'metrics': metrics,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'predictions': y_pred.tolist(),
            'true_labels': y_test.tolist()
        }
        
        if y_pred_proba is not None:
            evaluation_results['prediction_probabilities'] = y_pred_proba.tolist()
        
        # Print summary
        print(f"\n{model_name} Evaluation Results:")
        print(f"Accuracy: {metrics['accuracy']:.4f}")
        print(f"Precision: {metrics['precision']:.4f}")
        print(f"Recall: {metrics['recall']:.4f}")
        print(f"F1-Score: {metrics['f1_score']:.4f}")
        if 'auc' in metrics:
            print(f"AUC: {metrics['auc']:.4f}")
        
        print(f"\nConfusion Matrix:")
        print(f"True Negatives: {conf_matrix[0, 0]}")
        print(f"False Positives: {conf_matrix[0, 1]}")
        print(f"False Negatives: {conf_matrix[1, 0]}")
        print(f"True Positives: {conf_matrix[1, 1]}")
        
        return evaluation_results

    def plot_model_performance(self, evaluation_results, model_name="model", save_path=None):
        """
        Create visualization plots for model performance

        Args:
            evaluation_results: Results from evaluate_model
            model_name: Name of the model
            save_path: Path to save plots (optional)
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{model_name} Performance Analysis', fontsize=16)

        # Confusion Matrix
        conf_matrix = np.array(evaluation_results['confusion_matrix'])
        sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Normal', 'Attack'],
                   yticklabels=['Normal', 'Attack'], ax=axes[0, 0])
        axes[0, 0].set_title('Confusion Matrix')
        axes[0, 0].set_ylabel('True Label')
        axes[0, 0].set_xlabel('Predicted Label')

        # Metrics Bar Plot
        metrics = evaluation_results['metrics']
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())

        bars = axes[0, 1].bar(metric_names, metric_values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        axes[0, 1].set_title('Performance Metrics')
        axes[0, 1].set_ylabel('Score')
        axes[0, 1].set_ylim(0, 1)

        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')

        # ROC Curve (if probabilities available)
        if 'prediction_probabilities' in evaluation_results:
            y_true = evaluation_results['true_labels']
            y_proba = evaluation_results['prediction_probabilities']

            fpr, tpr, _ = roc_curve(y_true, y_proba)
            auc_score = roc_auc_score(y_true, y_proba)

            axes[1, 0].plot(fpr, tpr, color='darkorange', lw=2,
                           label=f'ROC curve (AUC = {auc_score:.3f})')
            axes[1, 0].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
            axes[1, 0].set_xlim([0.0, 1.0])
            axes[1, 0].set_ylim([0.0, 1.05])
            axes[1, 0].set_xlabel('False Positive Rate')
            axes[1, 0].set_ylabel('True Positive Rate')
            axes[1, 0].set_title('ROC Curve')
            axes[1, 0].legend(loc="lower right")
        else:
            axes[1, 0].text(0.5, 0.5, 'ROC Curve\nNot Available',
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('ROC Curve')

        # Class Distribution
        class_report = evaluation_results['classification_report']
        classes = ['Normal (0)', 'Attack (1)']
        support_values = [class_report['0']['support'], class_report['1']['support']]

        axes[1, 1].pie(support_values, labels=classes, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('Class Distribution in Test Set')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Performance plots saved to: {save_path}")

        plt.show()

    def compare_models(self, model_results):
        """
        Compare performance of multiple models

        Args:
            model_results: Dictionary of model results from training
        """
        logger.info("Comparing model performances...")

        # Extract metrics for comparison
        comparison_data = []
        for model_name, results in model_results.items():
            metrics = results['metrics']
            metrics['model'] = model_name
            comparison_data.append(metrics)

        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.set_index('model')

        # Print comparison table
        print("\nModel Comparison:")
        print("=" * 80)
        print(comparison_df.round(4))

        # Create comparison plot
        fig, ax = plt.subplots(figsize=(12, 6))

        metrics_to_plot = ['accuracy', 'precision', 'recall', 'f1_score']
        x = np.arange(len(comparison_df.index))
        width = 0.2

        for i, metric in enumerate(metrics_to_plot):
            if metric in comparison_df.columns:
                ax.bar(x + i * width, comparison_df[metric], width, label=metric.title())

        ax.set_xlabel('Models')
        ax.set_ylabel('Score')
        ax.set_title('Model Performance Comparison')
        ax.set_xticks(x + width * 1.5)
        ax.set_xticklabels(comparison_df.index)
        ax.legend()
        ax.set_ylim(0, 1)

        plt.tight_layout()
        plt.show()

        # Find best model
        best_model_name = comparison_df['f1_score'].idxmax()
        best_f1_score = comparison_df.loc[best_model_name, 'f1_score']

        logger.info(f"Best performing model: {best_model_name} (F1-Score: {best_f1_score:.4f})")

        return best_model_name, comparison_df

def train_complete_pipeline(data_file, test_size=0.2, save_models=True):
    """
    Complete training pipeline for NIDS models

    Args:
        data_file: Path to the dataset CSV file
        test_size: Proportion of data to use for testing
        save_models: Whether to save trained models

    Returns:
        dict: Training results and best model
    """
    logger.info("Starting complete NIDS training pipeline...")

    # Load data
    logger.info(f"Loading data from: {data_file}")
    df = pd.read_csv(data_file)

    # Import preprocessing module
    from data_preprocessing import DataPreprocessor

    # Initialize preprocessor
    preprocessor = DataPreprocessor()

    # Prepare features
    X, y = preprocessor.prepare_features(df, target_column='label', fit=True)

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=42, stratify=y
    )

    logger.info(f"Training set size: {X_train.shape[0]}")
    logger.info(f"Test set size: {X_test.shape[0]}")
    logger.info(f"Feature dimensions: {X_train.shape[1]}")

    # Initialize trainer
    trainer = NIDSModelTrainer()

    # Train supervised models
    supervised_results = trainer.train_supervised_models(X_train, X_test, y_train, y_test)

    # Train unsupervised models
    unsupervised_results = trainer.train_unsupervised_models(X_train, X_test, y_test)

    # Combine results
    all_results = {**supervised_results, **unsupervised_results}

    # Compare models
    best_model_name, comparison_df = trainer.compare_models(all_results)

    # Save models if requested
    if save_models:
        logger.info("Saving trained models...")

        # Save preprocessor
        preprocessor.save_preprocessor("models/preprocessor.joblib")

        # Save best model
        best_model = all_results[best_model_name]['model']
        best_metrics = all_results[best_model_name]['metrics']

        model_path, metadata_path = trainer.save_model(
            best_model, f"best_{best_model_name}", best_metrics
        )

        # Save all models
        for model_name, results in all_results.items():
            trainer.save_model(results['model'], model_name, results['metrics'])

    # Create performance plots for best model
    best_evaluation = trainer.evaluate_model(
        all_results[best_model_name]['model'],
        X_test, y_test,
        best_model_name
    )

    trainer.plot_model_performance(
        best_evaluation,
        best_model_name,
        save_path=f"models/{best_model_name}_performance.png"
    )

    return {
        'results': all_results,
        'best_model_name': best_model_name,
        'best_model': all_results[best_model_name]['model'],
        'preprocessor': preprocessor,
        'comparison': comparison_df,
        'evaluation': best_evaluation
    }

if __name__ == "__main__":
    # Example usage
    try:
        # Generate sample data if not exists
        from dataset_generator import NetworkDatasetGenerator

        generator = NetworkDatasetGenerator()
        df = generator.generate_dataset(num_normal=5000, num_attacks=1000)
        df.to_csv("training_data.csv", index=False)

        # Train models
        results = train_complete_pipeline("training_data.csv")

        logger.info("Training pipeline completed successfully!")
        logger.info(f"Best model: {results['best_model_name']}")

    except Exception as e:
        logger.error(f"Training pipeline failed: {e}")
        raise
