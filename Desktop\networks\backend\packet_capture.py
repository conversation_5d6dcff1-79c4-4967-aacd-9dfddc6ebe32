#!/usr/bin/env python3
"""
Network Packet Capture Module for NIDS
Uses Scapy to capture and analyze network packets in real-time
"""

import time
import csv
import threading
from datetime import datetime
from scapy.all import sniff, IP, TCP, UDP, ICMP
import netifaces
import logging
from collections import deque
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PacketCapture:
    def __init__(self, interface=None, max_packets=10000):
        """
        Initialize packet capture system
        
        Args:
            interface: Network interface to capture on (None for auto-detect)
            max_packets: Maximum packets to store in memory
        """
        self.interface = interface or self._get_default_interface()
        self.max_packets = max_packets
        self.captured_packets = deque(maxlen=max_packets)
        self.is_capturing = False
        self.capture_thread = None
        self.packet_count = 0
        
        # Feature extraction configuration
        self.features = [
            'timestamp', 'src_ip', 'dst_ip', 'src_port', 'dst_port',
            'protocol', 'payload_length', 'packet_direction', 'flags'
        ]
        
    def _get_default_interface(self):
        """Get the default network interface"""
        try:
            # Get default gateway interface
            gateways = netifaces.gateways()
            default_interface = gateways['default'][netifaces.AF_INET][1]
            logger.info(f"Using default interface: {default_interface}")
            return default_interface
        except Exception as e:
            logger.warning(f"Could not detect default interface: {e}")
            return None
    
    def extract_packet_features(self, packet):
        """
        Extract relevant features from a network packet
        
        Args:
            packet: Scapy packet object
            
        Returns:
            dict: Extracted features
        """
        features = {
            'timestamp': datetime.now().isoformat(),
            'src_ip': '',
            'dst_ip': '',
            'src_port': 0,
            'dst_port': 0,
            'protocol': 'OTHER',
            'payload_length': len(packet),
            'packet_direction': 'UNKNOWN',
            'flags': ''
        }
        
        try:
            # Extract IP layer information
            if IP in packet:
                ip_layer = packet[IP]
                features['src_ip'] = ip_layer.src
                features['dst_ip'] = ip_layer.dst
                
                # Determine packet direction (simplified)
                # This is a basic implementation - in practice, you'd need
                # to know your network topology
                local_networks = ['192.168.', '10.', '172.16.', '127.']
                src_is_local = any(features['src_ip'].startswith(net) for net in local_networks)
                dst_is_local = any(features['dst_ip'].startswith(net) for net in local_networks)
                
                if src_is_local and not dst_is_local:
                    features['packet_direction'] = 'OUTGOING'
                elif not src_is_local and dst_is_local:
                    features['packet_direction'] = 'INCOMING'
                else:
                    features['packet_direction'] = 'INTERNAL'
                
                # Extract transport layer information
                if TCP in packet:
                    tcp_layer = packet[TCP]
                    features['protocol'] = 'TCP'
                    features['src_port'] = tcp_layer.sport
                    features['dst_port'] = tcp_layer.dport
                    features['flags'] = str(tcp_layer.flags)
                    
                elif UDP in packet:
                    udp_layer = packet[UDP]
                    features['protocol'] = 'UDP'
                    features['src_port'] = udp_layer.sport
                    features['dst_port'] = udp_layer.dport
                    
                elif ICMP in packet:
                    features['protocol'] = 'ICMP'
                    # ICMP doesn't have ports
                    features['src_port'] = 0
                    features['dst_port'] = 0
                    
        except Exception as e:
            logger.error(f"Error extracting packet features: {e}")
            
        return features
    
    def packet_handler(self, packet):
        """
        Handle captured packets
        
        Args:
            packet: Scapy packet object
        """
        try:
            features = self.extract_packet_features(packet)
            self.captured_packets.append(features)
            self.packet_count += 1
            
            # Log every 100 packets
            if self.packet_count % 100 == 0:
                logger.info(f"Captured {self.packet_count} packets")
                
        except Exception as e:
            logger.error(f"Error handling packet: {e}")
    
    def start_capture(self, filter_str=""):
        """
        Start packet capture
        
        Args:
            filter_str: BPF filter string (e.g., "tcp port 80")
        """
        if self.is_capturing:
            logger.warning("Packet capture is already running")
            return
            
        self.is_capturing = True
        logger.info(f"Starting packet capture on interface: {self.interface}")
        logger.info(f"Filter: {filter_str if filter_str else 'None'}")
        
        def capture_worker():
            try:
                sniff(
                    iface=self.interface,
                    prn=self.packet_handler,
                    filter=filter_str,
                    stop_filter=lambda x: not self.is_capturing
                )
            except Exception as e:
                logger.error(f"Packet capture error: {e}")
                self.is_capturing = False
        
        self.capture_thread = threading.Thread(target=capture_worker, daemon=True)
        self.capture_thread.start()
    
    def stop_capture(self):
        """Stop packet capture"""
        if not self.is_capturing:
            logger.warning("Packet capture is not running")
            return
            
        logger.info("Stopping packet capture...")
        self.is_capturing = False
        
        if self.capture_thread:
            self.capture_thread.join(timeout=5)
            
        logger.info(f"Capture stopped. Total packets captured: {self.packet_count}")
    
    def save_to_csv(self, filename, include_labels=False):
        """
        Save captured packets to CSV file
        
        Args:
            filename: Output CSV filename
            include_labels: Whether to include a 'label' column (for training data)
        """
        if not self.captured_packets:
            logger.warning("No packets to save")
            return
            
        logger.info(f"Saving {len(self.captured_packets)} packets to {filename}")
        
        fieldnames = self.features.copy()
        if include_labels:
            fieldnames.append('label')
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for packet_data in self.captured_packets:
                if include_labels:
                    # Default to normal traffic (0) - this would need manual labeling
                    # or integration with attack detection logic
                    packet_data['label'] = 0
                writer.writerow(packet_data)
                
        logger.info(f"Packets saved to {filename}")
    
    def get_recent_packets(self, count=10):
        """
        Get the most recent captured packets
        
        Args:
            count: Number of recent packets to return
            
        Returns:
            list: Recent packet data
        """
        return list(self.captured_packets)[-count:] if self.captured_packets else []
    
    def get_statistics(self):
        """
        Get capture statistics
        
        Returns:
            dict: Statistics about captured packets
        """
        if not self.captured_packets:
            return {'total_packets': 0}
            
        protocols = {}
        directions = {}
        
        for packet in self.captured_packets:
            # Count protocols
            protocol = packet.get('protocol', 'OTHER')
            protocols[protocol] = protocols.get(protocol, 0) + 1
            
            # Count directions
            direction = packet.get('packet_direction', 'UNKNOWN')
            directions[direction] = directions.get(direction, 0) + 1
        
        return {
            'total_packets': len(self.captured_packets),
            'protocols': protocols,
            'directions': directions,
            'capture_active': self.is_capturing
        }

if __name__ == "__main__":
    # Example usage
    capture = PacketCapture()
    
    try:
        # Start capturing packets
        capture.start_capture()
        
        # Capture for 30 seconds
        time.sleep(30)
        
        # Stop capture and save data
        capture.stop_capture()
        capture.save_to_csv("captured_packets.csv", include_labels=True)
        
        # Print statistics
        stats = capture.get_statistics()
        print(f"Capture Statistics: {json.dumps(stats, indent=2)}")
        
    except KeyboardInterrupt:
        logger.info("Capture interrupted by user")
        capture.stop_capture()
    except Exception as e:
        logger.error(f"Capture failed: {e}")
